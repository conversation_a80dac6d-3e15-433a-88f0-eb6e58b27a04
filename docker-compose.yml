version: '3.8'

services:
  # Frontend application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload in development
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_MODE=real
      - VITE_API_BASE_URL=http://**********:8080/api/v1
      - VITE_COGNITO_HOSTED_UI_URL=${VITE_COGNITO_HOSTED_UI_URL}
      - VITE_COGNITO_CLIENT_ID=${VITE_COGNITO_CLIENT_ID}
      - VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}
      - VITE_COGNITO_REGION=${VITE_COGNITO_REGION}
      - VITE_COGNITO_REDIRECT_URI=${VITE_COGNITO_REDIRECT_URI:-http://localhost:3000/callback}
    extra_hosts:
      # Allow container to access host services
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

  # Mock API service (optional - only start when needed)
  api-mock:
    image: nginx:alpine
    ports:
      - "8081:80"
    volumes:
      - ./docker/api-mock:/usr/share/nginx/html
      - ./docker/nginx-api.conf:/etc/nginx/nginx.conf
    networks:
      - app-network
    restart: unless-stopped
    profiles:
      - mock-api

  # Production build
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "80:80"
    environment:
      - VITE_API_MODE=${VITE_API_MODE:-real}
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://**********:8080/api/v1}
      - VITE_COGNITO_HOSTED_UI_URL=${VITE_COGNITO_HOSTED_UI_URL}
      - VITE_COGNITO_CLIENT_ID=${VITE_COGNITO_CLIENT_ID}
      - VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}
      - VITE_COGNITO_REGION=${VITE_COGNITO_REGION}
      - VITE_COGNITO_REDIRECT_URI=${VITE_COGNITO_REDIRECT_URI:-http://localhost/callback}
    extra_hosts:
      # Allow container to access host services
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    profiles:
      - production

networks:
  app-network:
    driver: bridge

volumes:
  node_modules:
