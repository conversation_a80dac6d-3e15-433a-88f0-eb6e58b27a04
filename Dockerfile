# syntax=docker/dockerfile:1

# Multi-stage build for React + TypeScript + Vite application

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install dependencies with cache mount for better performance
RUN --mount=type=cache,target=/root/.npm \
  npm ci --only=production=false

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Install wget for health checks (if not already present)
RUN apk add --no-cache wget

# Create non-root user for better security
RUN addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy environment template for runtime environment variable injection
COPY env.template.js /usr/share/nginx/html/env.template.js
COPY docker-entrypoint.sh /docker-entrypoint.sh

# Make entrypoint script executable and set proper ownership
RUN chmod +x /docker-entrypoint.sh && \
  chown -R nextjs:nodejs /usr/share/nginx/html && \
  chown nextjs:nodejs /docker-entrypoint.sh

# Expose port 80
EXPOSE 80

# Health check with improved reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# Use custom entrypoint for environment variable injection
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]

# Development stage
FROM node:20-alpine AS development

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install all dependencies (including dev dependencies) with cache mount
RUN --mount=type=cache,target=/root/.npm \
  npm ci

# Copy source code
COPY . .

# Create non-root user for development
RUN addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001 && \
  chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose development port
EXPOSE 3000

# Start development server with host binding for Docker
CMD ["npm", "run", "dev"]
